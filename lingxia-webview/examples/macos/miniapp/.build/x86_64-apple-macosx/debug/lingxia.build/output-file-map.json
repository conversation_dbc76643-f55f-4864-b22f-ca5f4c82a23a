{"": {"swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/master.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/ColorExtensions.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxApp.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxAppBase.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/PlatformTypes.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/TabBarConfig.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBarConfig.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBarConfig.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBarConfig~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBarConfig.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/WebView.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/WebView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/SwiftBridgeCore.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/LxAppViewController_iOS.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_iOS.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_iOS.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_iOS~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_iOS.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/LxApp_iOS.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_iOS.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_iOS.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_iOS~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_iOS.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/NavigationBar_iOS.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar_iOS.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar_iOS.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar_iOS~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar_iOS.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/TabBar_iOS.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar_iOS.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar_iOS.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar_iOS~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar_iOS.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/LxAppViewController_macOS.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_macOS.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_macOS.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_macOS~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController_macOS.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/LxAppWindowController_macOS.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppWindowController_macOS.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppWindowController_macOS.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppWindowController_macOS~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppWindowController_macOS.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/LxApp_macOS.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_macOS.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_macOS.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_macOS~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp_macOS.swiftdeps"}}