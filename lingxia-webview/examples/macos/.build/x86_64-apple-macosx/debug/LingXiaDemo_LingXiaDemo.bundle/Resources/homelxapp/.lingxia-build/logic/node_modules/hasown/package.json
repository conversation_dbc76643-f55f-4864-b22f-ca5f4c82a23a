{"name": "hasown", "version": "2.0.2", "description": "A robust, ES3 compatible, \"has own property\" predicate.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "pretest": "npm run lint", "tsc": "tsc -p .", "posttsc": "attw -P", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/hasOwn.git"}, "keywords": ["has", "hasOwnProperty", "hasOwn", "has-own", "own", "has", "property", "in", "javascript", "ecmascript"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/hasOwn/issues"}, "homepage": "https://github.com/inspect-js/hasOwn#readme", "dependencies": {"function-bind": "^1.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.1", "@ljharb/eslint-config": "^21.1.0", "@ljharb/tsconfig": "^0.2.0", "@types/function-bind": "^1.1.10", "@types/mock-property": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test"]}}