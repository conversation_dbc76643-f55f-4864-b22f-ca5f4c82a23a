/* Mobile-First Todo App */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100vh;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    background-attachment: fixed;
    color: #2c3e50;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    font-size: 16px;
    position: relative;
    width: 100%;
    overflow-x: hidden;
    touch-action: pan-y;
}

/* Full screen background */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    z-index: -2;
    pointer-events: none;
}

/* Background todos text */
body::before {
    content: 'todos';
    position: fixed;
    top: 15%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 100px;
    font-weight: 100;
    color: rgba(255, 182, 193, 0.25);
    z-index: -1;
    pointer-events: none;
    user-select: none;
}

@media (max-width: 768px) {
    html::before {
        font-size: 80px;
        top: 15%;
    }
}

@media (max-width: 480px) {
    html::before {
        font-size: 60px;
        top: 12%;
    }
}

body {
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 100vh;
    touch-action: pan-y;
}

.todoapp {
    background: #fff;
    margin: 20px auto 20px;
    position: relative;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    max-width: calc(100% - 40px);
    min-width: 280px;
    border-radius: 8px;
    overflow: hidden;
    z-index: 10;
}

@media (max-width: 768px) {
    .todoapp {
        margin: 8px;
        border-radius: 8px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
    }
}

.todoapp input::-webkit-input-placeholder {
    font-style: italic;
    font-weight: 300;
    color: #e6e6e6;
}

.todoapp input::-moz-placeholder {
    font-style: italic;
    font-weight: 300;
    color: #e6e6e6;
}

.todoapp input::input-placeholder {
    font-style: italic;
    font-weight: 300;
    color: #e6e6e6;
}

.todoapp h1 {
    position: absolute;
    top: -80px;
    width: 100%;
    font-size: 60px;
    font-weight: 100;
    text-align: center;
    color: rgba(0, 123, 255, 0.4);
    -webkit-text-rendering: optimizeLegibility;
    -moz-text-rendering: optimizeLegibility;
    text-rendering: optimizeLegibility;
    user-select: none;
    pointer-events: none;
}

@media (max-width: 768px) {
    .todoapp h1 {
        top: -50px;
        font-size: 40px;
    }
}

.new-todo {
    position: relative;
    margin: 0;
    width: 100%;
    font-size: 18px;
    font-family: inherit;
    font-weight: inherit;
    line-height: 1.4em;
    border: none;
    color: inherit;
    padding: 16px 16px 16px 50px;
    background: rgba(0, 0, 0, 0.003);
    box-shadow: inset 0 -2px 1px rgba(0,0,0,0.03);
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@media (max-width: 768px) {
    .new-todo {
        font-size: 16px;
        padding: 14px 14px 14px 35px;
    }
}

.main {
    position: relative;
    z-index: 2;
    border-top: 1px solid #e6e6e6;
}

.toggle-all {
    width: 1px;
    height: 1px;
    border: none;
    opacity: 0;
    position: absolute;
    right: 100%;
    bottom: 100%;
}

.toggle-all + label {
    width: 50px;
    height: 30px;
    font-size: 0;
    position: absolute;
    top: -55px;
    left: -10px;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    cursor: pointer;
}

.toggle-all + label:before {
    content: '❯';
    font-size: 18px;
    color: #e6e6e6;
    padding: 8px 20px 8px 20px;
}

.toggle-all:checked + label:before {
    color: #737373;
}

@media (max-width: 768px) {
    .toggle-all + label {
        width: 40px;
        height: 25px;
        top: -45px;
        left: -8px;
    }

    .toggle-all + label:before {
        font-size: 16px;
        padding: 6px 15px 6px 15px;
    }
}

.todo-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.todo-list li {
    position: relative;
    font-size: 18px;
    border-bottom: 1px solid #ededed;
    min-height: 50px;
}

@media (max-width: 768px) {
    .todo-list li {
        font-size: 16px;
        min-height: 48px;
    }
}

.todo-list li:last-child {
    border-bottom: none;
}

.todo-list li.editing {
    border-bottom: none;
    padding: 0;
}

.todo-list li.editing .edit {
    display: block;
    width: calc(100% - 43px);
    padding: 12px 16px;
    margin: 0 0 0 43px;
}

.todo-list li.editing .view {
    display: none;
}

.todo-list li .toggle {
    text-align: center;
    width: 40px;
    height: auto;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 5px;
    margin: auto 0;
    border: none;
    -webkit-appearance: none;
    appearance: none;
    z-index: 2;
}

.todo-list li .toggle {
    opacity: 0;
}

.todo-list li .toggle + label {
    background-image: url('data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%22-10%20-18%20100%20135%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2250%22%20fill%3D%22none%22%20stroke%3D%22%23ededed%22%20stroke-width%3D%223%22/%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-position: center left;
}

.todo-list li .toggle:checked + label {
    background-image: url('data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%22-10%20-18%20100%20135%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2250%22%20fill%3D%22none%22%20stroke%3D%22%23bddad5%22%20stroke-width%3D%223%22/%3E%3Cpath%20fill%3D%22%235dc2af%22%20d%3D%22M72%2025L42%2071%2027%2056l-4%204%2020%2020%2034-52z%22/%3E%3C/svg%3E');
}

.todo-list li label {
    word-break: break-word;
    padding: 12px 50px 12px 55px;
    display: block;
    line-height: 1.4;
    font-weight: 400;
    color: #2c3e50;
    cursor: pointer;
}

@media (max-width: 768px) {
    .todo-list li label {
        padding: 12px 35px 12px 45px;
        line-height: 1.3;
    }
}

.todo-list li.completed label {
    color: #cdcdcd;
    text-decoration: line-through;
}

.todo-list li .destroy {
    display: none;
    position: absolute;
    top: 0;
    right: 8px;
    bottom: 0;
    width: 35px;
    height: 35px;
    margin: auto 0;
    font-size: 24px;
    color: #cc9a9a;
    transition: color 0.2s ease-out;
    cursor: pointer;
    background: none;
    border: none;
    border-radius: 50%;
}

.todo-list li .destroy:hover {
    color: #af5b5e;
    background: rgba(175, 91, 94, 0.1);
}

.todo-list li .destroy:after {
    content: '×';
}

.todo-list li:hover .destroy {
    display: block;
}

@media (max-width: 768px) {
    .todo-list li .destroy {
        display: block;
        width: 30px;
        height: 30px;
        font-size: 20px;
        right: 6px;
    }

    .todo-list li .destroy:hover {
        background: rgba(175, 91, 94, 0.15);
    }
}

.todo-list li .edit {
    display: none;
}

.footer {
    color: #6c757d;
    padding: 12px 15px;
    height: auto;
    min-height: 50px;
    text-align: center;
    border-top: 1px solid #e6e6e6;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    flex-wrap: wrap;
    z-index: 15;
}

@media (max-width: 768px) {
    .footer {
        padding: 10px 12px;
        min-height: 45px;
        gap: 6px;
        flex-direction: column;
        align-items: stretch;
    }
}

.todo-count {
    flex: 0 0 auto;
    min-width: 100px;
    text-align: left;
    white-space: nowrap;
    font-size: 14px;
    overflow: visible;
    z-index: 10;
    position: relative;
}

.todo-count strong {
    font-weight: 300;
}

@media (max-width: 768px) {
    .todo-count {
        text-align: center;
        min-width: auto;
        order: 1;
        margin-bottom: 8px;
    }
}

.filters {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    justify-content: center;
    flex: 1;
    gap: 4px;
}

.filters li {
    display: inline;
}

.filters li a {
    color: inherit;
    margin: 2px;
    padding: 8px 16px;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    z-index: 10;
    display: inline-block;
    min-width: 70px;
    text-align: center;
    font-size: 14px;
    transition: all 0.2s ease;
}

.filters li a:hover {
    border-color: rgba(0, 123, 255, 0.2);
    background: rgba(0, 123, 255, 0.1);
}

.filters li a.selected {
    border-color: rgba(0, 123, 255, 0.3);
    background: rgba(0, 123, 255, 0.15);
}

@media (max-width: 768px) {
    .filters {
        order: 2;
        margin-bottom: 8px;
    }

    .filters li a {
        padding: 10px 20px;
        margin: 1px;
        min-width: 80px;
        font-size: 15px;
    }
}

.clear-completed {
    flex: 1;
    text-align: right;
    line-height: 20px;
    text-decoration: none;
    cursor: pointer;
    background: none;
    border: none;
    color: #6c757d;
    white-space: nowrap;
    padding: 8px 12px;
    border-radius: 4px;
    z-index: 20;
    position: relative;
    pointer-events: auto;
}

.clear-completed:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

@media (max-width: 768px) {
    .clear-completed {
        text-align: center;
        order: 3;
        padding: 12px;
        font-size: 15px;
    }
}

/* Empty state styles */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 18px;
    margin-bottom: 8px;
    font-weight: 500;
}

.empty-state-hint {
    font-size: 14px;
    opacity: 0.7;
}

@media (max-width: 768px) {
    .empty-state {
        padding: 30px 15px;
    }

    .empty-state-icon {
        font-size: 40px;
        margin-bottom: 12px;
    }

    .empty-state-text {
        font-size: 16px;
    }

    .empty-state-hint {
        font-size: 13px;
    }
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
    .toggle-all,
    .todo-list li .toggle {
        background: none;
    }

    .todo-list li .toggle {
        height: 35px;
    }
}

@media (max-width: 480px) {
    .todoapp {
        margin: 5px;
    }

    .todoapp h1 {
        top: -40px;
        font-size: 32px;
    }

    .new-todo {
        font-size: 15px;
        padding: 12px 12px 12px 40px;
    }

    .todo-list li {
        font-size: 15px;
        min-height: 44px;
    }

    .todo-list li label {
        padding: 10px 30px 10px 40px;
    }

    .filters li a {
        padding: 8px 16px;
        min-width: 70px;
        font-size: 14px;
    }
}
