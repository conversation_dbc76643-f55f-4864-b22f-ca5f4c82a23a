{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "LingXia LxApp demo", "type": "module", "scripts": {"build": "lingxia build", "build:prod": "lingxia build --prod", "dev": "lingxia build --dev"}, "keywords": ["lingxia", "miniapp"], "author": "LingXia Team", "license": "MIT", "dependencies": {"lodash-es": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "vue": "^3.5.13", "clsx": "^2.1.1", "class-variance-authority": "^0.7.0", "@radix-ui/react-slot": "^1.1.0", "tailwindcss": "^3.4.17", "autoprefixer": "^10.4.21"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.7", "typescript": "^5.0.0"}}