import Foundation
import os.log

#if os(iOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Represents a single tab bar item with its configuration
public struct TabBarItem {
    /// The page path to navigate to when this tab is selected
    let pagePath: String
    /// Optional text label to display below the icon
    let text: String?
    /// Path to the icon image file for the unselected state
    let iconPath: String
    /// Path to the icon image file for the selected state
    let selectedIconPath: String
    /// Whether this tab is currently selected
    let selected: Bool
    /// Whether this tab should be visible in the tab bar
    let visible: Bool

    /// Initializes a new TabBarItem
    /// - Parameters:
    ///   - pagePath: The page path to navigate to when selected
    ///   - text: Optional text label (default: nil)
    ///   - iconPath: Path to the unselected state icon
    ///   - selectedIconPath: Path to the selected state icon
    ///   - selected: Whether this tab is selected (default: false)
    ///   - visible: Whether this tab is visible (default: true)
    public init(
        pagePath: String,
        text: String? = nil,
        iconPath: String,
        selectedIconPath: String,
        selected: Bool = false,
        visible: Bool = true
    ) {
        self.pagePath = pagePath
        self.text = text
        self.iconPath = iconPath
        self.selectedIconPath = selectedIconPath
        self.selected = selected
        self.visible = visible
    }
}

/// Configuration structure for the TabBar component
/// Defines the appearance, position, and behavior of the tab bar
public struct TabBarConfig {
    #if os(iOS)
    /// Background color of the tab bar. If nil, uses default color based on position
    let backgroundColor: UIColor?
    /// Color for selected tab items (text and icons)
    let selectedColor: UIColor?
    /// Color for unselected tab items (text and icons)
    let color: UIColor?
    /// Color for the tab bar border. If nil, uses default border color
    let borderStyle: UIColor?
    #elseif os(macOS)
    /// Background color of the tab bar. If nil, uses default color based on position
    let backgroundColor: NSColor?
    /// Color for selected tab items (text and icons)
    let selectedColor: NSColor?
    /// Color for unselected tab items (text and icons)
    let color: NSColor?
    /// Color for the tab bar border. If nil, uses default border color
    let borderStyle: NSColor?
    #endif
    
    /// Height of the tab bar in points. If nil, uses default height
    let height: CGFloat?
    /// Position of the tab bar relative to the content
    let position: Position
    /// Array of tab bar items to display
    let items: [TabBarItem]
    /// Whether the tab bar should be visible
    let hidden: Bool

    /// Enumeration defining possible positions for the tab bar
    public enum Position {
        /// Tab bar positioned at the top of the screen
        case top
        /// Tab bar positioned at the bottom of the screen
        case bottom
        /// Tab bar positioned on the left side of the screen
        case left
        /// Tab bar positioned on the right side of the screen
        case right
    }

    // MARK: - Default Colors
    #if os(iOS)
    /// Default color for selected tab items (#1677FF - Modern blue)
    static let DEFAULT_SELECTED_COLOR = UIColor(red: 0.09, green: 0.47, blue: 1.0, alpha: 1.0)
    /// Default color for unselected tab items (#666666 - Dark gray)
    static let DEFAULT_COLOR = UIColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0)
    /// Default border color (#F0F0F0 - Light gray)
    static let DEFAULT_BORDER_COLOR = UIColor(red: 0.94, green: 0.94, blue: 0.94, alpha: 1.0)
    /// Default background color (White)
    static let DEFAULT_BACKGROUND_COLOR = UIColor.white
    #elseif os(macOS)
    /// Default color for selected tab items (#1677FF - Modern blue)
    static let DEFAULT_SELECTED_COLOR = NSColor(red: 0.09, green: 0.47, blue: 1.0, alpha: 1.0)
    /// Default color for unselected tab items (#666666 - Dark gray)
    static let DEFAULT_COLOR = NSColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0)
    /// Default border color (#F0F0F0 - Light gray)
    static let DEFAULT_BORDER_COLOR = NSColor(red: 0.94, green: 0.94, blue: 0.94, alpha: 1.0)
    /// Default background color (White)
    static let DEFAULT_BACKGROUND_COLOR = NSColor.white
    #endif

    /// Initializes a new TabBarConfig with the specified parameters
    public init(
        #if os(iOS)
        backgroundColor: UIColor? = nil,
        selectedColor: UIColor? = nil,
        color: UIColor? = nil,
        borderStyle: UIColor? = nil,
        #elseif os(macOS)
        backgroundColor: NSColor? = nil,
        selectedColor: NSColor? = nil,
        color: NSColor? = nil,
        borderStyle: NSColor? = nil,
        #endif
        height: CGFloat? = nil,
        position: Position = .bottom,
        items: [TabBarItem] = [],
        hidden: Bool = false
    ) {
        self.backgroundColor = backgroundColor
        self.selectedColor = selectedColor
        self.color = color
        self.borderStyle = borderStyle
        self.height = height
        self.position = position
        self.items = items
        self.hidden = hidden
    }

    /// Creates a TabBarConfig from a JSON string
    /// - Parameter json: JSON string containing tab bar configuration
    /// - Returns: TabBarConfig instance if parsing succeeds, nil otherwise
    public static func fromJson(_ json: String?) -> TabBarConfig? {
        guard let json = json, !json.isEmpty else {
            return nil
        }

        do {
            guard let data = json.data(using: .utf8),
                  let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                os_log("TabBarConfig.fromJson: failed to parse JSON", log: OSLog(subsystem: "LingXia", category: "TabBar"), type: .error)
                return nil
            }

            let backgroundColorString = jsonObject["backgroundColor"] as? String
            let items: [TabBarItem] = (jsonObject["list"] as? [[String: Any]])?.compactMap { item in
                let finalText = item["text"] as? String

                return TabBarItem(
                    pagePath: item["pagePath"] as? String ?? "",
                    text: finalText?.isEmpty == false ? finalText : nil,
                    iconPath: item["iconPath"] as? String ?? "",
                    selectedIconPath: item["selectedIconPath"] as? String ?? "",
                    selected: item["selected"] as? Bool ?? false,
                    visible: item["visible"] as? Bool ?? true
                )
            } ?? []

            let positionString = jsonObject["position"] as? String ?? "bottom"
            let position: Position
            switch positionString.lowercased() {
            case "top": position = .top
            case "left": position = .left
            case "right": position = .right
            default: position = .bottom
            }

            let backgroundColor = parseColor(backgroundColorString)

            let config = TabBarConfig(
                backgroundColor: backgroundColor,
                selectedColor: parseColor(jsonObject["selectedColor"] as? String),
                color: parseColor(jsonObject["color"] as? String),
                borderStyle: parseColor(jsonObject["borderStyle"] as? String),
                height: jsonObject["height"] as? CGFloat,
                position: position,
                items: items,
                hidden: !(jsonObject["visible"] as? Bool ?? true)
            )

            return config
        } catch {
            os_log("TabBarConfig.fromJson: JSON parsing error: %@", log: OSLog(subsystem: "LingXia", category: "TabBar"), type: .error, error.localizedDescription)
            return nil
        }
    }

    #if os(iOS)
    private static func parseColor(_ colorString: String?) -> UIColor? {
        guard let colorString = colorString, !colorString.isEmpty else {
            return nil
        }

        if colorString.lowercased() == "transparent" {
            return UIColor.clear
        }

        if colorString.hasPrefix("rgba(") {
            return parseRgbaColor(colorString)
        }

        return UIColor(hexString: colorString)
    }

    private static func parseRgbaColor(_ rgba: String) -> UIColor? {
        let values = rgba.replacingOccurrences(of: "rgba(", with: "")
            .replacingOccurrences(of: ")", with: "")
            .components(separatedBy: ",")
            .map { $0.trimmingCharacters(in: .whitespaces) }

        guard values.count == 4,
              let r = Float(values[0]),
              let g = Float(values[1]),
              let b = Float(values[2]),
              let a = Float(values[3]) else {
            return nil
        }

        return UIColor(red: CGFloat(r/255.0), green: CGFloat(g/255.0), blue: CGFloat(b/255.0), alpha: CGFloat(a))
    }
    #elseif os(macOS)
    private static func parseColor(_ colorString: String?) -> NSColor? {
        guard let colorString = colorString, !colorString.isEmpty else {
            return nil
        }

        if colorString.lowercased() == "transparent" {
            return NSColor.clear
        }

        if colorString.hasPrefix("rgba(") {
            return parseRgbaColor(colorString)
        }

        return NSColor(hexString: colorString)
    }

    private static func parseRgbaColor(_ rgba: String) -> NSColor? {
        let values = rgba.replacingOccurrences(of: "rgba(", with: "")
            .replacingOccurrences(of: ")", with: "")
            .components(separatedBy: ",")
            .map { $0.trimmingCharacters(in: .whitespaces) }

        guard values.count == 4,
              let r = Float(values[0]),
              let g = Float(values[1]),
              let b = Float(values[2]),
              let a = Float(values[3]) else {
            return nil
        }

        return NSColor(red: CGFloat(r/255.0), green: CGFloat(g/255.0), blue: CGFloat(b/255.0), alpha: CGFloat(a))
    }
    #endif
}

#if os(iOS)
extension UIColor {
    convenience init?(hexString: String) {
        let hex = hexString.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int = UInt64()
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        self.init(red: CGFloat(r) / 255, green: CGFloat(g) / 255, blue: CGFloat(b) / 255, alpha: CGFloat(a) / 255)
    }
}
#elseif os(macOS)
extension NSColor {
    convenience init?(hexString: String) {
        let hex = hexString.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int = UInt64()
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        self.init(red: CGFloat(r) / 255, green: CGFloat(g) / 255, blue: CGFloat(b) / 255, alpha: CGFloat(a) / 255)
    }
}
#endif
