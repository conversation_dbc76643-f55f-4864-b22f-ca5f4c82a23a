import Foundation
import WebKit
import os.log
import CLingXiaFFI

#if os(iOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Notification action identifiers
public let ACTION_SWITCH_PAGE = "com.lingxia.SWITCH_PAGE_ACTION"

#if os(iOS)
public let ACTION_CLOSE_LXAPP = "com.lingxia.CLOSE_MINIAPP_ACTION"

/// LxApp launch mode for iOS
public enum LxAppLaunchMode {
    case replaceRoot
    case modal
}
#else
public let ACTION_CLOSE_LXAPP = "com.lingxia.CLOSE_LXAPP_ACTION"
#endif

/// Shared core LxApp management logic
@MainActor
public class SharedLxApp {
    nonisolated private static let log = OSLog(subsystem: "LingXia", category: "SharedLxApp")

    /// Singleton instance
    private static var instance: SharedLxApp?

    /// Home LxApp configuration
    internal static var homeLxAppId: String?
    internal static var homeLxAppInitialRoute: String?
    
    /// Active paths tracking
    private static var lastActivePaths: [String: String] = [:]
    
    #if os(iOS)
    /// Launch mode for iOS
    private static var launchMode: LxAppLaunchMode = .replaceRoot
    #elseif os(macOS)
    /// Window size for macOS
    private static var windowSize: (width: CGFloat, height: CGFloat) = (414, 896)
    /// Active window controllers for macOS
    private static var activeWindowControllers: [Any] = [] // Will be typed properly in platform-specific implementations
    #endif

    private init() {}

    /// Initialize the LxApp system
    public static func initialize() {
        if homeLxAppId != nil {
            os_log("SharedLxApp.initialize() already called, skipping", log: log, type: .info)
            return
        }
        performInitialization()
    }

    private static func performInitialization() {
        instance = SharedLxApp()

        #if os(iOS)
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path ?? ""
        let cachesPath = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""
        #elseif os(macOS)
        // Use Application Support directory for macOS
        let appSupportPath = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first?.path ?? ""
        let cachesSupportPath = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""
        
        let documentsPath = "\(appSupportPath)/LingXia"
        let cachesPath = "\(cachesSupportPath)/LingXia"
        
        // Create directories if they don't exist
        try? FileManager.default.createDirectory(atPath: documentsPath, withIntermediateDirectories: true, attributes: nil)
        try? FileManager.default.createDirectory(atPath: cachesPath, withIntermediateDirectories: true, attributes: nil)
        #endif

        let initResult = miniappInit(documentsPath, cachesPath)
        let initResultString = initResult?.toString()

        if let initResult = initResultString {
            let parts = initResult.components(separatedBy: ":")
            if parts.count >= 2 {
                homeLxAppId = parts[0]
                homeLxAppInitialRoute = Array(parts[1...]).joined(separator: ":")
                os_log("Initialized with home app: %@ at %@", log: log, type: .info, homeLxAppId!, homeLxAppInitialRoute!)
            } else {
                os_log("Failed to parse home LxApp details: %@", log: log, type: .error, initResult)
            }
        } else {
            os_log("Failed to get home LxApp details from native init", log: log, type: .error)
        }

        os_log("SharedLxApp initialized with documents: %@ caches: %@", log: log, type: .info, documentsPath, cachesPath)
    }

    /// Set home LxApp configuration
    public static func setHomeLxApp(appId: String, initialRoute: String = "/") {
        homeLxAppId = appId
        homeLxAppInitialRoute = initialRoute
        os_log("Home LxApp configured: %@ at %@", log: log, type: .info, appId, initialRoute)
    }

    /// Set home LxApp ID
    public static func setHomeLxAppId(_ appId: String) {
        homeLxAppId = appId
        os_log("Home LxApp ID set: %@", log: log, type: .info, appId)
    }

    /// Set home LxApp initial route
    public static func setHomeLxAppInitialRoute(_ route: String) {
        homeLxAppInitialRoute = route
        os_log("Home LxApp initial route set: %@", log: log, type: .info, route)
    }

    /// Get last active path for app
    public static func getLastActivePath(for appId: String) -> String {
        return lastActivePaths[appId] ?? "/"
    }

    /// Set last active path for app
    public static func setLastActivePath(_ path: String, for appId: String) {
        lastActivePaths[appId] = path
    }

    #if os(iOS)
    /// Set launch mode for iOS
    public static func setLaunchMode(_ mode: LxAppLaunchMode) {
        launchMode = mode
    }

    /// Get launch mode for iOS
    public static func getLaunchMode() -> LxAppLaunchMode {
        return launchMode
    }
    #elseif os(macOS)
    /// Set window size for macOS
    public static func setWindowSize(width: CGFloat, height: CGFloat) {
        windowSize = (width, height)
        os_log("Window size configured: %fx%f", log: log, type: .info, width, height)
    }

    /// Get window size for macOS
    internal static func getWindowSize() -> (width: CGFloat, height: CGFloat) {
        return windowSize
    }
    #endif

    /// Check if app is home LxApp
    public static func isHomeLxApp(_ appId: String) -> Bool {
        return appId == homeLxAppId
    }

    /// Get home LxApp ID
    public static func getHomeLxAppId() -> String? {
        return homeLxAppId
    }

    /// Get home LxApp initial route
    public static func getHomeLxAppInitialRoute() -> String {
        return homeLxAppInitialRoute ?? "/"
    }
}
