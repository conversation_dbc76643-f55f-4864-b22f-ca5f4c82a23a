import Foundation
import CLingXiaFFI

#if os(iOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Main LxApp interface - unified API for both iOS and macOS
/// This class provides a clean, consistent API that delegates to platform-specific implementations
@MainActor
public class LxApp {

    // MARK: - Core System Management

    /// Initialize the LxApp system
    public static func initialize() {
        LxAppCore.initialize()
    }

    /// Set home LxApp configuration
    public static func setHomeLxApp(appId: String, initialRoute: String = "/") {
        LxAppCore.setHomeLxApp(appId: appId, initialRoute: initialRoute)
    }

    // MARK: - Platform-Specific Configuration

    #if os(iOS)
    /// Set launch mode (iOS only)
    public static func setLaunchMode(_ mode: LxAppLaunchMode) {
        LxAppCore.setLaunchMode(mode)
    }

    /// Configure transparent system bars (iOS only)
    public static func configureTransparentSystemBars(viewController: UIViewController, lightStatusBarIcons: Bool = false) {
        LxAppPlatform.configureTransparentSystemBars(viewController: viewController, lightStatusBarIcons: lightStatusBarIcons)
    }
    #elseif os(macOS)
    /// Set window size (macOS only)
    public static func setWindowSize(width: CGFloat, height: CGFloat) {
        LxAppCore.setWindowSize(width: width, height: height)
    }
    #endif

    // MARK: - App Lifecycle Management

    /// Open home LxApp
    public static func openHomeLxApp() {
        LxAppPlatform.openHomeLxApp()
    }

    /// Open specific LxApp
    public static func openLxApp(appId: String, path: String = "/") {
        #if os(iOS)
        LxAppPlatform.openLxApp(appId: appId, path: path)
        #elseif os(macOS)
        LxAppPlatform.openLxAppInternal(appId: appId, path: path)
        #endif
    }

    /// Close LxApp
    public static func closeLxApp(appId: String) {
        #if os(iOS)
        LxAppPlatform.closeLxApp(appId: appId)
        #elseif os(macOS)
        LxAppPlatform.closeLxAppInternal(appId: appId)
        #endif
    }

    /// Switch to page in LxApp
    public static func switchPage(appId: String, path: String) {
        #if os(iOS)
        LxAppPlatform.switchPage(appId: appId, path: path)
        #elseif os(macOS)
        LxAppPlatform.switchPageInternal(appId: appId, path: path)
        #endif
    }
}

#if os(macOS)
extension LxApp {
    /// Open specific LxApp (FFI compatible)
    nonisolated public static func openLxApp(appid: RustStr, path: RustStr) -> Bool {
        return LxAppPlatform.openLxApp(appid: appid, path: path)
    }

    /// Close LxApp (FFI compatible)
    nonisolated public static func closeLxApp(appid: RustStr) -> Bool {
        return LxAppPlatform.closeLxApp(appid: appid)
    }

    /// Switch to page in LxApp (FFI compatible)
    nonisolated public static func switchPage(appid: RustStr, path: RustStr) -> Bool {
        return LxAppPlatform.switchPage(appid: appid, path: path)
    }
}
#endif

/// Platform-specific implementation
#if os(iOS)
typealias LxAppPlatform = iOSLxApp
#elseif os(macOS)
typealias LxAppPlatform = macOSLxApp
#endif
