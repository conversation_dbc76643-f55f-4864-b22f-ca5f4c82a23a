#if os(macOS)
import Foundation
import Cocoa
import os.log
import CLingXiaFFI

/// macOS directory provider implementation
public struct macOSDirectoryProvider: LxAppPlatformDirectoryProvider {
    public static func getDirectoryConfig() -> LxAppDirectoryConfig {
        let bundleId = Bundle.main.bundleIdentifier ?? "com.lingxia.app"

        let appSupportPath = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first?.path ?? ""
        let cachesSupportPath = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""

        let documentsPath = "\(appSupportPath)/\(bundleId)"
        let cachesPath = "\(cachesSupportPath)/\(bundleId)"

        // Create directories if they don't exist
        try? FileManager.default.createDirectory(atPath: documentsPath, withIntermediateDirectories: true, attributes: nil)
        try? FileManager.default.createDirectory(atPath: cachesPath, withIntermediateDirectories: true, attributes: nil)

        return LxAppDirectoryConfig(documentsPath: documentsPath, cachesPath: cachesPath)
    }
}

/// macOS-specific LxApp implementation
@MainActor
public class macOSLxApp {
    nonisolated(unsafe) private static let log = OSLog(subsystem: "LingXia", category: "macOSLxApp")
    
    private static var activeWindowControllers: [macOSLxAppWindowController] = []
    private static var hasInitialized = false
    
    /// Open home LxApp
    public static func openHomeLxApp() {
        guard let homeLxAppId = LxAppCore.getHomeLxAppId() else {
            os_log("Home LxApp not configured", log: log, type: .error)
            return
        }

        let initialRoute = LxAppCore.getHomeLxAppInitialRoute()
        openLxApp(appId: homeLxAppId, path: initialRoute)
    }
    
    /// Open specific LxApp (String version for convenience)
    public static func openLxApp(appId: String, path: String) {
        print("DEBUG: openLxAppInternal called for appId: \(appId) path: \(path)")
        os_log("openLxAppInternal: appId=%@ path=%@", log: log, type: .info, appId, path)

        // Initialize LxApps if not already done
        macOSLxApp.initializeLxAppsIfNeeded()

        // Check if window already exists for this app
        if let existingController = activeWindowControllers.first(where: { $0.appId == appId }) {
            print("DEBUG: Found existing window controller for appId: \(appId)")
            existingController.window?.makeKeyAndOrderFront(nil as Any?)
            switchPage(appId: appId, path: path)
            return
        }

        // Get the last active path for state restoration (like iOS)
        let actualPath: String
        let storedPath = LxAppCore.getLastActivePath(for: appId)

        if !storedPath.isEmpty && storedPath != path && appId != LxAppCore.getHomeLxAppId() {
            actualPath = storedPath
            os_log("openLxAppInternal: Using stored path for state restoration: %@ (requested: %@)",
                   log: log, type: .info, actualPath, path)
        } else {
            actualPath = path
            os_log("openLxAppInternal: Using requested path: %@", log: log, type: .info, actualPath)
        }

        // Call onMiniappOpened FIRST to ensure WebView is created before window setup (like iOS)
        os_log("🔧 Calling onMiniappOpened for appId: %@ path: %@", log: log, type: .info, appId, actualPath)
        let openResult = onMiniappOpened(appId, actualPath)
        os_log("onMiniappOpened completed with result=%d for appId=%@ path=%@", log: log, type: .info, openResult, appId, actualPath)

        // Create window controller with the actual path
        print("DEBUG: Creating window controller for appId: \(appId) path: \(actualPath)")
        let windowController = macOSLxAppWindowController(appId: appId, path: actualPath)

        // Show the window explicitly
        DispatchQueue.main.async {
            print("DEBUG: Showing window for appId: \(appId)")
            windowController.showWindow(nil as Any?)
            windowController.window?.makeKeyAndOrderFront(nil as Any?)
            
            // Activate app
            NSApp.activate(ignoringOtherApps: true)
            
            // Log window state
            if let window = windowController.window {
                print("DEBUG: Window visible: \(window.isVisible)")
                print("DEBUG: Window key window: \(window.isKeyWindow)")
                print("DEBUG: Window frame: \(NSStringFromRect(window.frame))")
            }
        }
        
        activeWindowControllers.append(windowController)

        os_log("Window controller created and window shown", log: log, type: .info)
    }
    
    /// Close LxApp (String version for convenience)
    public static func closeLxApp(appId: String) {
        if let controller = activeWindowControllers.first(where: { $0.appId == appId }) {
            controller.window?.close()
        }
    }

    /// Switch to page in LxApp (String version for convenience)
    public static func switchPage(appId: String, path: String) {
        if let controller = activeWindowControllers.first(where: { $0.appId == appId }),
           let viewController = controller.window?.contentViewController as? macOSLxAppViewController {
            viewController.switchPage(targetPath: path)
        }
    }

    // MARK: - FFI Compatible Methods

    /// Open specific LxApp (FFI compatible version)
    nonisolated public static func openLxApp(appid: RustStr, path: RustStr) -> Bool {
        let appId = appid.toString()
        let pathString = path.toString()
        os_log("Opening LxApp: %@ at path: %@", log: log, type: .info, appId, pathString)

        // Check if we're already on main thread to avoid deadlock
        if Thread.isMainThread {
            MainActor.assumeIsolated {
                openLxApp(appId: appId, path: pathString)
            }
        } else {
            DispatchQueue.main.sync {
                openLxApp(appId: appId, path: pathString)
            }
        }
        return true
    }

    /// Close LxApp (FFI compatible version)
    nonisolated public static func closeLxApp(appid: RustStr) -> Bool {
        let appId = appid.toString()
        if Thread.isMainThread {
            MainActor.assumeIsolated {
                closeLxApp(appId: appId)
            }
        } else {
            DispatchQueue.main.sync {
                closeLxApp(appId: appId)
            }
        }
        return true
    }

    /// Switch to page in LxApp (FFI compatible version)
    nonisolated public static func switchPage(appid: RustStr, path: RustStr) -> Bool {
        let appId = appid.toString()
        let pathString = path.toString()
        if Thread.isMainThread {
            MainActor.assumeIsolated {
                switchPage(appId: appId, path: pathString)
            }
        } else {
            DispatchQueue.main.sync {
                switchPage(appId: appId, path: pathString)
            }
        }
        return true
    }
    
    /// Remove window controller from active list
    internal static func removeWindowController(_ controller: macOSLxAppWindowController) {
        activeWindowControllers.removeAll { $0 === controller }
    }
    
    /// Get active window controllers
    internal static func getActiveWindowControllers() -> [macOSLxAppWindowController] {
        return activeWindowControllers
    }

    // MARK: - LxApps Initialization

    /// Initialize LxApps system if not already initialized
    public static func initializeLxAppsIfNeeded() {
        // Check if already initialized
        if isInitialized {
            return
        }

        print("Initializing LxApps system...")

        // Set platform directory provider
        LxAppCore.setPlatformDirectoryProvider(macOSDirectoryProvider.self)

        // Use LxAppCore.initialize() instead of duplicating the logic
        LxAppCore.initialize()

        // Check if initialization was successful
        if LxAppCore.getHomeLxAppId() != nil {
            print("✅ LxApps initialized successfully")
            _isInitialized = true
        } else {
            print("❌ Failed to initialize LxApps - no home app ID")
        }
    }

    /// Flag to track initialization state
    private static var _isInitialized = false

    /// Public getter for initialization state
    public static var isInitialized: Bool {
        return _isInitialized
    }
}

#endif
