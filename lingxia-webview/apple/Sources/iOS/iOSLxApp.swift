#if os(iOS)
import UIKit
import os.log

/// iOS directory provider implementation
public struct iOSDirectoryProvider: LxAppPlatformDirectoryProvider {
    public static func getDirectoryConfig() -> LxAppDirectoryConfig {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path ?? ""
        let cachesPath = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""

        return LxAppDirectoryConfig(documentsPath: documentsPath, cachesPath: cachesPath)
    }
}

/// iOS-specific LxApp implementation
@MainActor
public class iOSLxApp {
    private static let log = OSLog(subsystem: "LingXia", category: "iOSLxApp")
    
    /// Initialize LxApps system if needed
    public static func initializeLxAppsIfNeeded() {
        // Set platform directory provider
        LxAppCore.setPlatformDirectoryProvider(iOSDirectoryProvider.self)

        // Initialize the core system
        LxAppCore.initialize()
    }

    /// Open home LxApp
    public static func openHomeLxApp() {
        // Ensure initialization
        initializeLxAppsIfNeeded()

        guard let homeLxAppId = LxAppCore.getHomeLxAppId() else {
            os_log("Home LxApp not configured", log: log, type: .error)
            return
        }

        let initialRoute = LxAppCore.getHomeLxAppInitialRoute()
        openLxApp(appId: homeLxAppId, path: initialRoute)
    }
    
    /// Open specific LxApp
    public static func openLxApp(appId: String, path: String = "/") {
        os_log("Opening LxApp: %@ at path: %@", log: log, type: .info, appId, path)
        
        let viewController = iOSLxAppViewController(appId: appId, path: path)
        
        switch LxAppCore.getLaunchMode() {
        case .replaceRoot:
            replaceRootViewController(with: viewController)
        case .modal:
            presentModally(viewController)
        }
    }
    
    private static func replaceRootViewController(with viewController: UIViewController) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            os_log("No window found for root replacement", log: log, type: .error)
            return
        }
        
        window.rootViewController = viewController
        window.makeKeyAndVisible()
    }
    
    private static func presentModally(_ viewController: UIViewController) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            os_log("No root view controller found for modal presentation", log: log, type: .error)
            return
        }
        
        viewController.modalPresentationStyle = .fullScreen
        rootViewController.present(viewController, animated: true)
    }
    
    /// Close LxApp
    public static func closeLxApp(appId: String) {
        NotificationCenter.default.post(
            name: NSNotification.Name(ACTION_CLOSE_LXAPP),
            object: nil,
            userInfo: ["appId": appId]
        )
    }
    
    /// Switch to page in LxApp
    public static func switchPage(appId: String, path: String) {
        NotificationCenter.default.post(
            name: NSNotification.Name(ACTION_SWITCH_PAGE),
            object: nil,
            userInfo: ["appId": appId, "path": path]
        )
    }
    
    /// Configure transparent system bars
    public static func configureTransparentSystemBars(viewController: UIViewController, lightStatusBarIcons: Bool = false) {
        if #available(iOS 13.0, *) {
            viewController.overrideUserInterfaceStyle = lightStatusBarIcons ? .light : .dark
        }

        if let navController = viewController.navigationController {
            navController.navigationBar.setBackgroundImage(UIImage(), for: .default)
            navController.navigationBar.shadowImage = UIImage()
            navController.navigationBar.isTranslucent = true
            navController.navigationBar.backgroundColor = .clear
        }
    }
}

#endif
